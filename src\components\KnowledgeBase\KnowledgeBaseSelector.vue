<template>
  <div class="kb-selector">
    <el-select
      v-model="selectedKbId"
      :placeholder="placeholder"
      :loading="loading"
      :disabled="disabled"
      @change="handleChange"
      :style="{ width: width }"
      clearable
    >
      <el-option
        v-for="kb in knowledgeBases"
        :key="kb.id"
        :label="kb.name"
        :value="kb.id"
      >
        <div class="kb-option">
          <el-avatar :size="24" :src="kb.avatar" class="kb-avatar">
            <el-icon><Collection /></el-icon>
          </el-avatar>
          <div class="kb-info">
            <div class="kb-name">{{ kb.name }}</div>
            <div class="kb-description">{{ kb.description || '暂无描述' }}</div>
          </div>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Collection } from '@element-plus/icons-vue';
import {
  getKnowledgeBaseList,
  type KnowledgeBase
} from '/@/api/iot/knowledgeBase';

// Props
interface Props {
  modelValue?: string;
  placeholder?: string;
  width?: string;
  disabled?: boolean;
  autoLoad?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择知识库',
  width: '200px',
  disabled: false,
  autoLoad: true
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'change': [value: string, knowledgeBase: KnowledgeBase | null];
}>();

// 响应式数据
const selectedKbId = ref(props.modelValue || '');
const knowledgeBases = ref<KnowledgeBase[]>([]);
const loading = ref(false);

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedKbId.value = newValue || '';
});

// 监听内部值变化
watch(selectedKbId, (newValue) => {
  emit('update:modelValue', newValue);
});

// 方法
const loadKnowledgeBases = async () => {
  try {
    loading.value = true;
    const response = await getKnowledgeBaseList({
      page: 1,
      page_size: 100
    });

    const businessData = response.data;
    if (businessData.code === 200 && businessData.data) {
      knowledgeBases.value = businessData.data;
    } else {
      ElMessage.error(businessData.msg || businessData.message || '获取知识库列表失败');
    }
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    ElMessage.error('获取知识库列表失败');
  } finally {
    loading.value = false;
  }
};

const handleChange = (value: string) => {
  const selectedKb = knowledgeBases.value.find(kb => kb.id === value) || null;
  emit('change', value, selectedKb);
};

// 暴露方法给父组件
const refresh = () => {
  loadKnowledgeBases();
};

defineExpose({
  refresh,
  knowledgeBases
});

// 生命周期
onMounted(() => {
  if (props.autoLoad) {
    loadKnowledgeBases();
  }
});
</script>

<style scoped>
.kb-selector {
  display: inline-block;
}

.kb-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.kb-avatar {
  flex-shrink: 0;
}

.kb-info {
  flex: 1;
  min-width: 0;
}

.kb-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.kb-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}
</style>
